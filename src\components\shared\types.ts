import type { IAsset } from "webgi";

export type QueueItemState = "idle" | "processing" | "queued" | "downloading" | "uploading" | "finished" | "error" | "cancelled";
export interface QueueItem {
    file: File;
    data?: any;
    state: QueueItemState;
    progress: number;
}

// type Permission = "read" | "write" | "none" | undefined 

export interface fileMetaData {
  pub?: boolean;
  w?: string[];
  r?: string[];
  size?: number;
  type?: string;
  [key: string]: any;
}

export interface AppConfig {
  "app-name"?: string;
  logo?: string;
  favicon?: string;
  api?: string;
  "files-api"?: string;
  "webgi-version"?: string;
  "viewer-path"?: string;
  "editor-path"?: string;
  "assets-path"?: string;
  "app-version"?: string;
  "date-format"?: string;
  language?: string;
  "guest-redirection"?: string;
  "share-domains"?: string[];
  "diamond-key"?: string;
  plan?: {
    name: string;
    "storage-limit-models": number;
    "file-size-limit-mb": number;
    "allowed-file-types": string[];
    "allowed-features": string[];
  };
  features?: {
    encryption: {
      enabled: boolean;
      "default-password": string;
    };
    "public-sharing": {
      enabled: boolean;
    };
    "setup-user": { // to call /api/setup-drive for new users once they verify their email
      enabled: boolean;
    };
    "singup" : {
      enabled: boolean;
    };
    "edit-file-info" : {
      enabled: boolean;
    };
    "delete-file" : { // enabled by default
      enabled: boolean;
    };
    "delete-folder" : { // disabled by default, doesn't count empty folders
      enabled: boolean;
    };
    [key : string]: { enabled: boolean; [key: string]: any };
  };
  [key: string]: any;
}

type Preset = {
  basePath: string;
  assets: string[];
};

export interface AssetsLibrary {
  presets?: {
    Background?: Preset;
    Environment?: Preset;
    GemEnvironment?: Preset;
    Ground?: Preset;
    VJSON?: Preset;
    ModelStage?: Preset;
  };
  materials?: {
    metal?: Preset;
    gem?: Preset;
    ceramic?: Preset;
    pearl?: Preset;
    other?: Preset;
  };
  version?: string;
  [key: string]: any;
}

export type UserPlan = "free" | "start-up" | "premium" | "business" | "enterprise";

export type User = {
  id: string;
  email: string;
  role: string;
  user: string;
  verified: boolean;
  meta?: {
    base: string | null;
    plan?: UserPlan;
    assets?: string;
    share?: string[];
    [key: string]: any;
  };
}


export type ASSET_ITEM = {
    isCustom?: boolean;
    meta: {
        asset_data?: Record<string, any>;
        name: string;
    };
};
export type CUSTOM_ASSET_ITEM = ASSET_ITEM & IAsset;
export type CUSTOM_ASSET_KEYS = "Environment" | "GemEnvironment" | "Background" | "VJSON" | "Ground" | "Logo" | "Material" 
export type EMBED = {
    initialSettings?: {
        [x in string]: CUSTOM_ASSET_ITEM[];
    };
    baseurl?: string;
    previewBaseurl?: string;
    handleUploadLogo?: (data: {
        file: File;
        [x: string]: any;
    }) => Promise<null | CUSTOM_ASSET_ITEM>;
    getProjectCountByLogoUrl?: (logoUrl: string) => Promise<any>;
    handleProjectLogoUpdate?: (data: {
        isRemove?: boolean;
        isRemoveAsset?: boolean;
        item: CUSTOM_ASSET_ITEM;
        [x: string]: any;
    }) => Promise<any>;
    getlogoList?: () => Promise<null | CUSTOM_ASSET_ITEM[]>;
    maxLogoUpload?: number;
    enabled?: boolean;
    warnningMessage?: string;
    canRemoveHologram?: boolean;
};
export type CUSTOM_ASSET = {
    getCustomAssets?: () => Promise<null | Record<CUSTOM_ASSET_KEYS, CUSTOM_ASSET_ITEM[]>>;
    handleUploadAsset?: (data: {
        assetFile: File;
        assetType: string;
        materialCategory?: string;
        [x: string]: any;
    }) => Promise<null | CUSTOM_ASSET_ITEM>;
    handleArchiveAsset?: (data: {
        asset: any;
        isArchive: boolean;
        [x: string]: any;
    }) => Promise<null | CUSTOM_ASSET_ITEM>;
};

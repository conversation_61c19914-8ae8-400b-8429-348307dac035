import { CustomFileData } from "../api/api";
import assets from "../assets/assets.json"
/**
 * Asset mapping from old asset names to new asset names
 * This maps the old drive asset references to the new asset pack names
 */
const ASSET_MAPPING: Record<string, string> = {
  // Metal materials mapping - full paths
  'materials/metal/gold-white-750.pmat': '1-metal-whitegold-polished.pmat',
  'materials/metal/gold-yellow-750.pmat': '1-metal-gold-polished.pmat',
  'materials/metal/gold-rose-750.pmat': '1-metal-rosegold-polished.pmat',
  'materials/metal/platinum-950.pmat': '2-metal-platinum-polished.pmat',
  'materials/metal/silver-925.pmat': '3-metal-silver-polished.pmat',

  // Metal materials mapping - filename only
  'gold-white-750.pmat': '1-metal-whitegold-polished.pmat',
  'gold-yellow-750.pmat': '1-metal-gold-polished.pmat',
  'gold-rose-750.pmat': '1-metal-rosegold-polished.pmat',
  'platinum-950.pmat': '2-metal-platinum-polished.pmat',
  'silver-925.pmat': '3-metal-silver-polished.pmat',

  // Alternative metal naming patterns
  'whitegold.pmat': '1-metal-whitegold-polished.pmat',
  'yellowgold.pmat': '1-metal-gold-polished.pmat',
  'rosegold.pmat': '1-metal-rosegold-polished.pmat',
  'platinum.pmat': '2-metal-platinum-polished.pmat',
  'silver.pmat': '3-metal-silver-polished.pmat',

  // Gem materials mapping - full paths
  'materials/gem/diamond-white-1.dmat': '1-gem-diamond-white-1.dmat',
  'materials/gem/diamond-white-2.dmat': '2-gem-diamond-white-2.dmat',
  'materials/gem/diamond-white-3.dmat': '3-gem-diamond-white-3.dmat',
  'materials/gem/emerald-1.dmat': '4-gem-emerald-1.dmat',
  'materials/gem/ruby-1.dmat': '5-gem-ruby-1.dmat',
  'materials/gem/sapphire-1.dmat': '6-gem-sapphire-1.dmat',

  // Gem materials mapping - filename only
  'diamond-white-1.dmat': '1-gem-diamond-white-1.dmat',
  'diamond-white-2.dmat': '2-gem-diamond-white-2.dmat',
  'diamond-white-3.dmat': '3-gem-diamond-white-3.dmat',
  'emerald-1.dmat': '4-gem-emerald-1.dmat',
  'ruby-1.dmat': '5-gem-ruby-1.dmat',
  'sapphire-1.dmat': '6-gem-sapphire-1.dmat',

  // Alternative gem naming patterns
  'diamond.dmat': '1-gem-diamond-white-1.dmat',
  'emerald.dmat': '4-gem-emerald-1.dmat',
  'ruby.dmat': '5-gem-ruby-1.dmat',
  'sapphire.dmat': '6-gem-sapphire-1.dmat',

  // Environment/HDRI mappings
  'hdrmaps/studio.exr': 'studio-lighting.exr',
  'hdrmaps/outdoor.exr': 'outdoor-lighting.exr',
  'studio.exr': 'studio-lighting.exr',
  'outdoor.exr': 'outdoor-lighting.exr',

  // Add more mappings as needed based on the old drive structure
  // These can be extended based on actual old drive asset names found
};

/**
 * Process a 3D file config to update asset references to use new asset pack URLs
 * @param config The original config object from the old drive
 * @param assetFiles Array of asset files from the new drive
 * @param getDownloadUrl Function to get download URL for an asset file
 * @returns Processed config with updated asset URLs
 */
export function processConfig(
  config: any,
  assetFiles: CustomFileData[],
  getDownloadUrl: (file: CustomFileData) => string
): any {
  if (!config || typeof config !== 'object') {
    return config;
  }

  // Create a map of asset names to their file objects for quick lookup
  const assetMap = new Map<string, CustomFileData>();
  assetFiles.forEach(asset => {
    if (asset.name) {
      assetMap.set(asset.name, asset);
    }
  });

  console.log(`Config processor: Processing config with ${assetFiles.length} available assets`);
  console.log('Available assets:', Array.from(assetMap.keys()));

  // Deep clone the config to avoid mutating the original
  const processedConfig = JSON.parse(JSON.stringify(config));

  // Process materialConfig
  if (processedConfig.materialConfig?.materials) {
    processedConfig.materialConfig.materials = processedConfig.materialConfig.materials.map((material: any) => {
      if (material.path) {
        const newAssetName = mapAssetPath(material.path);
        const assetFile = assetMap.get(newAssetName);
        if (assetFile) {
          return {
            ...material,
            path: getDownloadUrl(assetFile)
          };
        }
      }
      return material;
    });
  }

  // Process configuratorConfig
  if (processedConfig.configuratorConfig && Array.isArray(processedConfig.configuratorConfig)) {
    processedConfig.configuratorConfig = processedConfig.configuratorConfig.map((configItem: any) => {
      if (configItem.options && Array.isArray(configItem.options)) {
        configItem.options = configItem.options.map((option: any) => {
          if (option.name) {
            const newAssetName = mapAssetPath(option.name);
            const assetFile = assetMap.get(newAssetName);
            if (assetFile) {
              return {
                ...option,
                name: getDownloadUrl(assetFile)
              };
            }
          }
          return option;
        });
      }
      return configItem;
    });
  }

  // Process sceneConfig if it exists
  if (processedConfig.sceneConfig) {
    processedConfig.sceneConfig = processObjectPaths(processedConfig.sceneConfig, assetMap, getDownloadUrl);
  }


  // Process any other top-level properties that might contain asset paths
  Object.keys(processedConfig).forEach(key => {
    if (typeof processedConfig[key] === 'string' && isAssetPath(processedConfig[key])) {
      const newAssetName = mapAssetPath(processedConfig[key]);
      const assetFile = assetMap.get(newAssetName);
      if (assetFile) {
        processedConfig[key] = getDownloadUrl(assetFile);
      }
    }
  });

  return processedConfig;
}

/**
 * Recursively process an object to update asset paths
 */
function processObjectPaths(
  obj: any,
  assetMap: Map<string, CustomFileData>,
  getDownloadUrl: (file: CustomFileData) => string
): any {
  if (!obj || typeof obj !== 'object') {
    return obj;
  }

  if (Array.isArray(obj)) {
    return obj.map(item => processObjectPaths(item, assetMap, getDownloadUrl));
  }

  const processed: any = {};
  Object.keys(obj).forEach(key => {
    const value = obj[key];
    if (typeof value === 'string' && isAssetPath(value)) {
      const newAssetName = mapAssetPath(value);
      const assetFile = assetMap.get(newAssetName);
      if (assetFile) {
        processed[key] = getDownloadUrl(assetFile);
      } else {
        processed[key] = value; // Keep original if no mapping found
      }
    } else if (typeof value === 'object') {
      processed[key] = processObjectPaths(value, assetMap, getDownloadUrl);
    } else {
      processed[key] = value;
    }
  });

  return processed;
}

/**
 * Map an old asset path to a new asset name using the mapping table
 */
function mapAssetPath(oldPath: string): string {
  // First try direct mapping
  if (ASSET_MAPPING[oldPath]) {
    console.log(`Asset mapping: ${oldPath} -> ${ASSET_MAPPING[oldPath]}`);
    return ASSET_MAPPING[oldPath];
  }

  // Extract filename from path and try mapping
  const filename = oldPath.split('/').pop() || oldPath;
  if (ASSET_MAPPING[filename]) {
    console.log(`Asset mapping (filename): ${oldPath} -> ${ASSET_MAPPING[filename]}`);
    return ASSET_MAPPING[filename];
  }

  // If no mapping found, return the filename as-is
  // This allows for assets that might already have the correct name
  console.log(`Asset mapping: No mapping found for ${oldPath}, using filename: ${filename}`);
  return filename;
}

/**
 * Check if a string looks like an asset path
 */
function isAssetPath(str: string): boolean {
  if (!str || typeof str !== 'string') {
    return false;
  }

  // Check for common asset file extensions
  const assetExtensions = ['.pmat', '.dmat', '.exr', '.hdr', '.jpg', '.png', '.svg', '.glb', '.gltf'];
  const hasAssetExtension = assetExtensions.some(ext => str.toLowerCase().endsWith(ext));
  
  // Check for asset-like paths
  const hasAssetPath = str.includes('materials/') || str.includes('hdrmaps/') || 
                      str.includes('images/') || str.includes('presets/') ||
                      str.includes('configs/');

  // Check for URLs that might be asset references
  const isAssetUrl = str.startsWith('http') && hasAssetExtension;

  return hasAssetExtension || hasAssetPath || isAssetUrl;
}

/**
 * Add or update asset mappings
 * This can be used to extend the mapping table dynamically
 */
export function addAssetMapping(oldPath: string, newAssetName: string): void {
  ASSET_MAPPING[oldPath] = newAssetName;
}

/**
 * Get all current asset mappings
 */
export function getAssetMappings(): Record<string, string> {
  return { ...ASSET_MAPPING };
}


//for testing
const convertConfig = (config : any)=>{
  return processConfig(config , assets , (file: CustomFileData) => "https://packs.ijewel3d.com/files/" + file.file);
} 

(window as any).convertConfig = convertConfig
/* eslint-disable @typescript-eslint/no-unused-vars */
import { createContext, createElement, useCallback, useContext, useEffect, useRef, useState } from "react";
import { CustomFileData, FileAPI } from "../api/api";
import { AppConfig, CUSTOM_ASSET, CUSTOM_ASSET_ITEM, CUSTOM_ASSET_KEYS, EMBED } from "../components/shared/types";
import { useUser } from "./UserProvider";
import { useBrowser } from "./BrowserProvider";
import { InstanceAssetsManager } from "./InstanceAssetsManager";

const AssetsContext = createContext({
  getCustomAssets: async () => new Promise<Record<string, CUSTOM_ASSET_ITEM[]> | null>(() => {}),
  handleUploadCustomAsset: async (data: any) => new Promise<CUSTOM_ASSET_ITEM | null>(() => {}),
  handleArchiveAsset: async (data: { asset: any; isArchive: boolean }) => new Promise<CUSTOM_ASSET_ITEM | null>(() => {}),
  getDefaultEnvMapUrl: async () => new Promise<string | undefined>(() => {}),
  handleUploadLogo: async (data: any) => new Promise<CUSTOM_ASSET_ITEM | null>(() => {}),
  getlogoList: async () => new Promise<CUSTOM_ASSET_ITEM[] | null>(() => {}),
  getInstanceAssetsLibrary: async () => new Promise<any>(() => {}),
  getInstanceAssets: async () => new Promise<any>(() => {}),
});

export const useAssets = () => useContext(AssetsContext);

function useSetupAssets() {
  const { api , config , user, isEnterpriseClient } = useUser();
  const { getThumbnailGenerator } = useBrowser();
  const parentFolder = useRef<CustomFileData | null>(null);
  const instanceAssetsManager = useRef<InstanceAssetsManager | null>(null);

  const getInstanceAssetsManager = useCallback(() => {
    if (!api) return null;
    if (!instanceAssetsManager.current) {
      instanceAssetsManager.current = new InstanceAssetsManager(api);
      if(config?.["assets-path"]){
        instanceAssetsManager.current.instanceAssetsFolderId = config?.["assets-path"]
      }
    }
    return instanceAssetsManager.current;
  }, [api, config]);

  const sanitizeCustomAsset = useCallback((asset: CustomFileData): CUSTOM_ASSET_ITEM | null => {
    const asset_url = api!.getDownloadUrl(asset);
    if (!asset_url) {
      console.error("Asset url not found", asset);
      return null;
    }

    const isMaterial = asset.tags?.includes("_materialCategory");
    const materialCategory = isMaterial ? asset.tags?.split(",").find((tag) => tag.includes("_materialCategory"))?.split("=")[1] : null;

    const meta :any = {
      asset_data: asset.config,
      name: asset.name,
    }
    
    if (isMaterial) {
      if(!meta.asset_data) meta.asset_data = {};
      meta.asset_data.materialCategory = materialCategory;
    }
    
    return {
      id: asset.id,
      path: asset_url,
      size: asset.size,
      name: asset.name,
      icon: api!.getDownloadUrl({ id: asset.id, file: asset.thumb }),
      isCustom: true,
      meta,
    };
  }, [api]);

  const getCustomAssetsFolder = useCallback(async () => {
    if(parentFolder.current) return parentFolder.current;
    if (!api) return null;
    const { data, error } = await api.getUserCustomAssetsFolder();
    if (!data || error) return null;
    parentFolder.current = data;
    return data;
  }, [api]);

  const [defaultAssetsType] = useState<CUSTOM_ASSET_KEYS[]>(["Background", "Environment", "GemEnvironment", "Ground", "VJSON", "Logo" , "Material"]);


  const getCustomAssets: CUSTOM_ASSET["getCustomAssets"] = async () => {
    if (!api || !api.user) return null;

    if (isEnterpriseClient()) {
      const manager = getInstanceAssetsManager();
      if (!manager) return null;

      const instanceAssets = await manager.getInstanceAssets();
      const assets: Record<string, CUSTOM_ASSET_ITEM[]> = {};

      instanceAssets.forEach((asset: CustomFileData) => {
        if (isArchived(asset)) return;

        const asset_type = getAssetType(asset);
        if (!asset_type) return;

        const finalAsset = manager.sanitizeCustomAsset(asset);
        if (!finalAsset) return;

        if (assets[asset_type]) {
          assets[asset_type].push(finalAsset);
        } else {
          assets[asset_type] = [finalAsset];
        }
      });

      return assets;
    }

    const { data, error } = await api.getUserCustomAssets();
    if (!data || error) return null;

    const assets: Record<string, CUSTOM_ASSET_ITEM[]> = {};

    (data ?? []).forEach((asset) => {
      if (isArchived(asset)) return;

      const asset_type = getAssetType(asset);
      if (!asset_type) return;

      const finalAsset = sanitizeCustomAsset(asset);
      if (!finalAsset) return;

      if (assets[asset_type]) {
        assets[asset_type].push(finalAsset);
      } else {
        assets[asset_type] = [finalAsset];
      }
    });

    return assets;
  };

  const handleUploadCustomAsset: CUSTOM_ASSET["handleUploadAsset"] = useCallback(
    async (data: { assetFile: File; assetType: string; materialCategory?: string }) => {
      if (!api) return null;
      const { assetFile, assetType , materialCategory } = data;

      const thumbGen = await getThumbnailGenerator();
      let thumbFile = null;
      if (thumbGen) {
        thumbFile = await thumbGen.snapFile(assetFile);

        if (thumbFile) {
          thumbFile = updateFileName({ file: thumbFile, suffix: new Date().getTime() });
        }
      }

      const newFile = updateFileName({ file: assetFile, suffix: new Date().getTime() });

      if (isEnterpriseClient()) {
        const manager = getInstanceAssetsManager();
        if (!manager) return null;

        const asset = await manager.uploadAsset(
          newFile,
          assetType as CUSTOM_ASSET_KEYS,
          materialCategory,
          thumbFile ?? undefined
        );

        if (!asset) return null;
        return manager.sanitizeCustomAsset(asset);
      }

      const parent = await getCustomAssetsFolder();
      if (!parent) return null;

      const {data : asset , error} = await api.uploadFile(
        newFile,
        parent.path + parent.id + "/",
        thumbFile ?? undefined,
        undefined,
        undefined,
        undefined,
        "_cstype=" + assetType + (materialCategory ? ",_materialCategory=" + materialCategory : ""),
      );

      if (!asset || error) return null;

      return sanitizeCustomAsset(asset);
    },
    [api, getCustomAssetsFolder, getThumbnailGenerator, sanitizeCustomAsset, isEnterpriseClient, getInstanceAssetsManager]
  );

  const handleArchiveAsset: CUSTOM_ASSET['handleArchiveAsset'] = async (data: {
    asset: any;
    isArchive: boolean;
  }) => {
    if(!api) return null
    const { asset, isArchive } = data;
    const {data : file, error} = await api.getFile(asset.id);

    if(!file || error){
      console.error("File not found", asset , error);
      return null;
    }

    const {data : updatedFile, error : uploadError} = await api.updateFile({id: file.id, tags: (isArchive ? "_archived" : "") + "," + file.tags})

    if(!updatedFile || uploadError){
      console.error("Error updating file", file , uploadError);
      return null;
    }

    if (isEnterpriseClient()) {
      const manager = getInstanceAssetsManager();
      if (manager) {
        return manager.sanitizeCustomAsset(updatedFile);
      }
    }

    return sanitizeCustomAsset(updatedFile);
  };

  const getDefaultEnvMapUrl = useCallback(async () => {
    if(!api) return;
    const assetsPath = config?.["assets-path"]; // id

    if(!assetsPath) return "https://packs.ijewel3d.com/files/env_gem_002_30251392af.exr"
    const { data , error} = await api.getAssets(config?.["assets-path"]);
    

    if(error || !data || data.length === 0) {
      return "https://packs.ijewel3d.com/files/env_gem_002_30251392af.exr";
      // console.error("Error getting assets", error);
      // return;
    }

    const mainFolder = data.find((file : CustomFileData) => file.name === "hdrmaps");
    if(!mainFolder) return;
    const children = data.filter((file : CustomFileData) => file.path.includes(mainFolder.id));
    const envMap = children.find((file : CustomFileData) => file.tags?.includes("metal") && file.tags?.includes("_default"));
    if(!envMap) {
      console.error("Default env map not set");
      return;
    }
    return api.getDownloadUrl(envMap)
  }, [api, config]);

  const isArchived = (asset: CustomFileData) => {
    return asset?.tags?.includes("_archived");
  };

  const getAssetType = (asset: CustomFileData): CUSTOM_ASSET_KEYS | null => {
    //the tag will come in the form _cstype=Environment
    const tags = (asset?.tags ?? "").split(",");
    const type = tags.find((t) => defaultAssetsType.includes(t.split("=")[1] as CUSTOM_ASSET_KEYS));
    if (type) return type.split("=")[1] as CUSTOM_ASSET_KEYS;
    return null;
  };

  

  const updateFileName = (data: { file: File; prefix?: string | number; suffix?: string | number; delimiter?: string }) => {
    const { file, prefix, suffix, delimiter = "_" } = data;
    const fileNameParts = file.name.split(".");
    const fileName = fileNameParts[0];
    const fileExtension = fileNameParts[1];

    let newFileName = fileName;
    if (prefix) newFileName = prefix + delimiter + newFileName;
    if (suffix) newFileName = newFileName + delimiter + suffix;
    return new File([file], `${newFileName}.${fileExtension}`, {
      type: file.type,
    });
  };

  
  const handleUploadLogo: EMBED['handleUploadLogo'] = async (data: any) => {
    if (!api) return null;
    return handleUploadCustomAsset({assetFile: data.file, assetType: "Logo"});
  }

  const getlogoList: EMBED['getlogoList'] = async () => {
    return getCustomAssets()?.then((assets) => {
      return assets?.["Logo"] ?? [];
    })
  }

  const getInstanceAssetsLibrary = useCallback(async () => {
    if (!isEnterpriseClient()) return null;

    const manager = getInstanceAssetsManager();
    if (!manager) return null;

    const instanceAssets = await manager.getInstanceAssets();
    return manager.processFiles(instanceAssets);
  }, [isEnterpriseClient, getInstanceAssetsManager]);

  const getInstanceAssets = useCallback(async () => {
    if (!isEnterpriseClient()) return null;

    const manager = getInstanceAssetsManager();
    if (!manager) return null;

    return await manager.getInstanceAssets();
  }, [isEnterpriseClient, getInstanceAssetsManager]);

  return {
    getCustomAssets,
    handleUploadCustomAsset,
    handleArchiveAsset,
    getDefaultEnvMapUrl,
    handleUploadLogo,
    getlogoList,
    getInstanceAssetsLibrary,
    getInstanceAssets
  };
}

export function AssetsProvider({ children }: { children: any }) {
  const value = useSetupAssets();
  return createElement(AssetsContext.Provider, { value }, children);
}
